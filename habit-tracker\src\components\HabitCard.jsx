import { useState } from 'react';
import Calendar from './Calendar.jsx';

const HabitCard = ({ 
  habit, 
  currentMonth, 
  onDelete, 
  onUpdateName, 
  onToggleCompletion, 
  isCompleted, 
  getStats 
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(habit.name);
  const [error, setError] = useState('');

  const stats = getStats();

  const handleNameEdit = () => {
    setIsEditing(true);
    setEditName(habit.name);
    setError('');
  };

  const handleNameSave = () => {
    const result = onUpdateName(editName);
    if (result.success) {
      setIsEditing(false);
      setError('');
    } else {
      setError(result.error);
    }
  };

  const handleNameCancel = () => {
    setIsEditing(false);
    setEditName(habit.name);
    setError('');
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleNameSave();
    } else if (e.key === 'Escape') {
      handleNameCancel();
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
      {/* Header with habit name and delete button */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          {isEditing ? (
            <div>
              <input
                type="text"
                value={editName}
                onChange={(e) => setEditName(e.target.value)}
                onKeyDown={handleKeyPress}
                onBlur={handleNameSave}
                className="text-xl font-semibold text-gray-900 dark:text-white bg-transparent border-b-2 border-blue-500 focus:outline-none focus:border-blue-600 w-full"
                autoFocus
                maxLength={50}
              />
              {error && (
                <p className="text-red-500 dark:text-red-400 text-sm mt-1">{error}</p>
              )}
            </div>
          ) : (
            <h3
              className="text-xl font-semibold text-gray-900 dark:text-white cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              onClick={handleNameEdit}
              title="Click to edit habit name"
            >
              {habit.name}
            </h3>
          )}
        </div>
        
        <button
          onClick={onDelete}
          className="text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400 transition-colors duration-200 ml-2"
          title="Delete habit"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{stats.currentStreak}</div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Current Streak</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {stats.monthlyTotal}/{stats.totalDays}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">This Month</div>
        </div>
      </div>

      {/* Calendar */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
        <Calendar
          currentMonth={currentMonth}
          onDateClick={onToggleCompletion}
          isDateCompleted={isCompleted}
          habitId={habit.id}
        />
      </div>

      {/* Longest streak (stretch goal) */}
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="text-center">
          <div className="text-lg font-semibold text-purple-600 dark:text-purple-400">{stats.longestStreak}</div>
          <div className="text-xs text-gray-500 dark:text-gray-400">Longest Streak</div>
        </div>
      </div>
    </div>
  );
};

export default HabitCard;
