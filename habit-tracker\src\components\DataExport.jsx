import { useState } from 'react';
import { getHabitData } from '../utils/localStorage.js';

const DataExport = () => {
  const [isExporting, setIsExporting] = useState(false);

  const exportData = async (format = 'json') => {
    setIsExporting(true);
    
    try {
      const data = getHabitData();
      const timestamp = new Date().toISOString().split('T')[0];
      
      let content, filename, mimeType;
      
      if (format === 'json') {
        content = JSON.stringify(data, null, 2);
        filename = `habit-tracker-data-${timestamp}.json`;
        mimeType = 'application/json';
      } else if (format === 'csv') {
        content = convertToCSV(data);
        filename = `habit-tracker-data-${timestamp}.csv`;
        mimeType = 'text/csv';
      }
      
      // Create and download file
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Export failed:', error);
      alert('Failed to export data. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const convertToCSV = (data) => {
    const { habits, completions } = data;
    
    // Create CSV header
    let csv = 'Habit Name,Date,Completed,Created At\n';
    
    // Add data rows
    habits.forEach(habit => {
      const habitCompletions = completions[habit.id] || {};
      
      // If no completions, add one row with habit info
      if (Object.keys(habitCompletions).length === 0) {
        csv += `"${habit.name}",,,${habit.createdAt}\n`;
      } else {
        // Add row for each completion date
        Object.entries(habitCompletions).forEach(([date, completed]) => {
          csv += `"${habit.name}",${date},${completed ? 'Yes' : 'No'},${habit.createdAt}\n`;
        });
      }
    });
    
    return csv;
  };

  const importData = (event) => {
    const file = event.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedData = JSON.parse(e.target.result);
        
        // Validate data structure
        if (!importedData.habits || !Array.isArray(importedData.habits)) {
          throw new Error('Invalid data format');
        }
        
        // Confirm import
        const confirmImport = window.confirm(
          `This will replace all current data with ${importedData.habits.length} habits. Are you sure?`
        );
        
        if (confirmImport) {
          localStorage.setItem('habit-tracker-data', JSON.stringify(importedData));
          window.location.reload(); // Reload to refresh the app
        }
      } catch (error) {
        console.error('Import failed:', error);
        alert('Failed to import data. Please check the file format.');
      }
    };
    
    reader.readAsText(file);
    event.target.value = ''; // Reset file input
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Data Management
      </h3>
      
      <div className="space-y-4">
        {/* Export Section */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Export Your Data
          </h4>
          <div className="flex gap-2">
            <button
              onClick={() => exportData('json')}
              disabled={isExporting}
              className="px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white text-sm rounded-lg transition-colors duration-200 flex items-center gap-2"
            >
              {isExporting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              )}
              Export JSON
            </button>
            
            <button
              onClick={() => exportData('csv')}
              disabled={isExporting}
              className="px-3 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white text-sm rounded-lg transition-colors duration-200 flex items-center gap-2"
            >
              {isExporting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              )}
              Export CSV
            </button>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Download your habit data for backup or analysis
          </p>
        </div>
        
        {/* Import Section */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Import Data
          </h4>
          <input
            type="file"
            accept=".json"
            onChange={importData}
            className="block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900 dark:file:text-blue-300"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Import previously exported JSON data (this will replace all current data)
          </p>
        </div>
        
        {/* Warning */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
          <div className="flex">
            <svg className="w-5 h-5 text-yellow-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div>
              <h5 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Data Safety
              </h5>
              <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
                Always export your data before importing new data. Importing will permanently replace all current habits and progress.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataExport;
