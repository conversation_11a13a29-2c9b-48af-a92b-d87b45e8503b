<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Habit Tracker Tests</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Habit Tracker Test Suite</h1>
        <p>This page tests the core functionality of the Habit Tracker application.</p>
        
        <button onclick="runTests()">Run All Tests</button>
        <button onclick="clearOutput()">Clear Output</button>
        <button onclick="testLocalStorage()">Test LocalStorage</button>
        
        <div id="output"></div>
    </div>

    <script type="module">
        // Mock console.log to capture output
        const originalLog = console.log;
        const outputDiv = document.getElementById('output');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            outputDiv.textContent += args.join(' ') + '\n';
            outputDiv.scrollTop = outputDiv.scrollHeight;
        };

        // Import test functions
        import { runAllTests } from './src/test/testUtils.js';
        
        window.runTests = () => {
            outputDiv.textContent = '';
            console.log('Starting Habit Tracker Tests...\n');
            const success = runAllTests();
            console.log(success ? '\n✅ All tests passed!' : '\n❌ Some tests failed!');
        };
        
        window.clearOutput = () => {
            outputDiv.textContent = '';
        };
        
        window.testLocalStorage = () => {
            outputDiv.textContent = '';
            console.log('Testing LocalStorage functionality...\n');
            
            // Test localStorage availability
            if (typeof Storage !== "undefined") {
                console.log('✅ LocalStorage is available');
                
                // Test basic operations
                localStorage.setItem('test-key', JSON.stringify({test: 'data'}));
                const retrieved = JSON.parse(localStorage.getItem('test-key'));
                console.log('✅ LocalStorage read/write works:', retrieved.test === 'data');
                
                localStorage.removeItem('test-key');
                console.log('✅ LocalStorage cleanup works');
            } else {
                console.log('❌ LocalStorage is not available');
            }
        };
    </script>
</body>
</html>
