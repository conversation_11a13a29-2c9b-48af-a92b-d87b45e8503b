import { useState } from 'react';
import { useHabits } from './hooks/useHabits.js';
import HabitCard from './components/HabitCard.jsx';
import AddHabitForm from './components/AddHabitForm.jsx';
import ThemeToggle from './components/ThemeToggle.jsx';
import DataExport from './components/DataExport.jsx';

function App() {
  const [showAddForm, setShowAddForm] = useState(false);
  const [showDataExport, setShowDataExport] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const { habits, isLoading, error, addHabit, deleteHabit, updateHabitName, toggleCompletion, isCompleted, getStats } = useHabits();

  const handleAddHabit = (name) => {
    const result = addHabit(name);
    if (result.success) {
      setShowAddForm(false);
    }
    return result;
  };

  const handleDeleteHabit = (habitId) => {
    if (window.confirm('Are you sure you want to delete this habit? This action cannot be undone.')) {
      return deleteHabit(habitId);
    }
    return { success: false };
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading your habits...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Habit Tracker</h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">Build better habits, one day at a time</p>
            </div>
            <div className="flex items-center gap-3">
              <ThemeToggle />
              <button
                onClick={() => setShowAddForm(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Add Habit
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
            <p className="font-medium">Error</p>
            <p className="text-sm">{error}</p>
          </div>
        )}

        {habits.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
              <svg className="w-12 h-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">No habits yet</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">Start building better habits by adding your first one!</p>
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
            >
              Add Your First Habit
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {habits.map(habit => (
              <HabitCard
                key={habit.id}
                habit={habit}
                currentMonth={currentMonth}
                onDelete={() => handleDeleteHabit(habit.id)}
                onUpdateName={(newName) => updateHabitName(habit.id, newName)}
                onToggleCompletion={(date) => toggleCompletion(habit.id, date)}
                isCompleted={(date) => isCompleted(habit.id, date)}
                getStats={() => getStats(habit.id, currentMonth)}
              />
            ))}
          </div>
        )}

        {/* Data Export Section */}
        {habits.length > 0 && (
          <div className="mt-12 border-t border-gray-200 dark:border-gray-700 pt-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Data Management
              </h2>
              <button
                onClick={() => setShowDataExport(!showDataExport)}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center gap-1"
              >
                {showDataExport ? 'Hide' : 'Show'} Options
                <svg
                  className={`w-4 h-4 transition-transform ${showDataExport ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </div>

            {showDataExport && (
              <div className="max-w-2xl">
                <DataExport />
              </div>
            )}
          </div>
        )}
      </main>

      {/* Add Habit Modal */}
      {showAddForm && (
        <AddHabitForm
          onAdd={handleAddHabit}
          onCancel={() => setShowAddForm(false)}
        />
      )}
    </div>
  );
}

export default App;
