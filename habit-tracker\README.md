# 🎯 Habit Tracker

A modern, responsive personal habit tracking web application built with React and TailwindCSS. Track your daily habits, visualize your progress, and build better routines with streak tracking and calendar views.

## ✨ Features

### Core Functionality
- **Add Habits**: Create new habits with custom names
- **Delete Habits**: Remove habits with confirmation dialog
- **Edit Habit Names**: Click on habit names to edit them inline
- **Calendar View**: Interactive monthly calendar for each habit
- **Progress Tracking**: Visual indicators for completed and pending days

### Progress Metrics
- **Current Streak**: Count of consecutive completed days
- **Monthly Total**: Days completed in the current month
- **Longest Streak**: Historical best streak for each habit
- **Visual Progress**: Color-coded calendar with completion status

### Data Persistence
- **LocalStorage**: All data persists across browser sessions
- **Real-time Updates**: Changes are saved automatically
- **Data Recovery**: Robust error handling for data operations

### User Experience
- **Responsive Design**: Works seamlessly on desktop and mobile
- **Modern UI**: Clean, minimal design with TailwindCSS
- **Interactive Elements**: Hover effects and smooth transitions
- **Accessibility**: Keyboard navigation and screen reader support

## 🚀 Getting Started

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd habit-tracker
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## 🏗️ Project Structure

```
habit-tracker/
├── src/
│   ├── components/          # React components
│   │   ├── AddHabitForm.jsx # Modal for adding new habits
│   │   ├── Calendar.jsx     # Monthly calendar component
│   │   └── HabitCard.jsx    # Individual habit display
│   ├── hooks/               # Custom React hooks
│   │   └── useHabits.js     # Habit management logic
│   ├── utils/               # Utility functions
│   │   ├── dateUtils.js     # Date manipulation helpers
│   │   ├── habitUtils.js    # Habit-specific utilities
│   │   └── localStorage.js  # LocalStorage operations
│   ├── test/                # Test utilities
│   │   └── testUtils.js     # Testing functions
│   ├── App.jsx              # Main application component
│   ├── main.jsx             # Application entry point
│   └── index.css            # Global styles (TailwindCSS)
├── public/                  # Static assets
├── test.html               # Test page for functionality
└── README.md               # This file
```

## 🎮 How to Use

### Adding Your First Habit
1. Click the "Add Habit" button in the header
2. Enter a descriptive name for your habit (e.g., "Exercise", "Read 10 pages", "Meditate")
3. Click "Add Habit" to save

### Tracking Progress
1. Click on any day in the calendar to mark it as completed
2. Completed days appear in green
3. Today is highlighted with a blue border
4. Future dates are disabled

### Managing Habits
- **Edit Names**: Click on any habit name to edit it inline
- **Delete Habits**: Click the trash icon and confirm deletion
- **View Stats**: Current streak, monthly total, and longest streak are displayed on each card

### Navigation
- Use the arrow buttons to navigate between months
- Click "Today" to return to the current month
- The calendar shows a 6-week grid with proper month boundaries

## 🧪 Testing

The application includes a comprehensive test suite. To run tests:

1. **Open the test page**: Navigate to `http://localhost:5173/test.html`
2. **Run tests**: Click "Run All Tests" to execute the test suite
3. **View results**: Test output appears in the console-style display

### Test Coverage
- Habit creation and validation
- Date utility functions
- Calendar grid generation
- Streak calculations
- LocalStorage operations
- Edge cases (leap years, month boundaries)

## 🛠️ Technical Details

### Built With
- **React 18**: Modern React with hooks
- **Vite**: Fast build tool and dev server
- **TailwindCSS**: Utility-first CSS framework
- **LocalStorage API**: Client-side data persistence

### Key Components
- **useHabits Hook**: Centralized habit state management
- **Calendar Component**: Interactive monthly calendar
- **HabitCard Component**: Individual habit display with stats
- **AddHabitForm Component**: Modal for creating new habits

### Data Structure
```javascript
{
  habits: [
    {
      id: "habit_1234567890_abc123",
      name: "Exercise",
      createdAt: "2024-01-01T00:00:00.000Z",
      isActive: true
    }
  ],
  completions: {
    "habit_1234567890_abc123": {
      "2024-01-01": true,
      "2024-01-02": true,
      "2024-01-03": false
    }
  },
  lastUpdated: "2024-01-03T12:00:00.000Z"
}
```

## 🚀 Future Enhancements

### Planned Features
- **Theme Toggle**: Light/dark mode support
- **Habit Categories**: Group habits by type (Health, Learning, etc.)
- **Data Export**: Download habit data as JSON/CSV
- **Statistics Dashboard**: Weekly/monthly completion percentages
- **Habit Reminders**: Browser notifications
- **Habit Templates**: Pre-defined habit suggestions
- **Goal Setting**: Target streaks and completion rates

### Potential Improvements
- **Cloud Sync**: Sync data across devices
- **Social Features**: Share progress with friends
- **Advanced Analytics**: Detailed progress charts
- **Habit Chains**: Link related habits together
- **Reward System**: Gamification elements

## 📱 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- Built with [Vite](https://vitejs.dev/) and [React](https://reactjs.org/)
- Styled with [TailwindCSS](https://tailwindcss.com/)
- Icons from [Heroicons](https://heroicons.com/)
- Inspired by habit tracking methodologies and modern web design principles
