import { useState } from 'react';
import { 
  getCalendarGrid, 
  getMonthName, 
  getYear, 
  isSameDay, 
  isToday,
  getFirstDayOfMonth,
  getLastDayOfMonth
} from '../utils/dateUtils.js';

const Calendar = ({ currentMonth, onDateClick, isDateCompleted, habitId }) => {
  const [displayMonth, setDisplayMonth] = useState(currentMonth);

  const calendarGrid = getCalendarGrid(displayMonth, 0); // Start week on Sunday
  const monthName = getMonthName(displayMonth);
  const year = getYear(displayMonth);
  const firstDayOfMonth = getFirstDayOfMonth(displayMonth);
  const lastDayOfMonth = getLastDayOfMonth(displayMonth);

  const goToPreviousMonth = () => {
    setDisplayMonth(new Date(displayMonth.getFullYear(), displayMonth.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setDisplayMonth(new Date(displayMonth.getFullYear(), displayMonth.getMonth() + 1, 1));
  };

  const goToCurrentMonth = () => {
    setDisplayMonth(new Date());
  };

  const handleDateClick = (date) => {
    // Only allow clicking on dates within the current display month
    if (date >= firstDayOfMonth && date <= lastDayOfMonth) {
      onDateClick(date);
    }
  };

  const getDayClasses = (date) => {
    const baseClasses = "w-8 h-8 flex items-center justify-center text-sm rounded-full cursor-pointer transition-all duration-200";
    const isCurrentMonth = date >= firstDayOfMonth && date <= lastDayOfMonth;
    const isCompleted = isDateCompleted(date);
    const isTodayDate = isToday(date);
    const isFutureDate = date > new Date();

    let classes = baseClasses;

    if (!isCurrentMonth) {
      // Dates from previous/next month
      classes += " text-gray-300 dark:text-gray-600 cursor-not-allowed";
    } else if (isFutureDate) {
      // Future dates
      classes += " text-gray-400 dark:text-gray-500 cursor-not-allowed";
    } else if (isCompleted) {
      // Completed dates
      classes += " bg-green-500 dark:bg-green-600 text-white hover:bg-green-600 dark:hover:bg-green-700";
    } else if (isTodayDate) {
      // Today (not completed)
      classes += " bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 border-2 border-blue-500 dark:border-blue-400 hover:bg-blue-200 dark:hover:bg-blue-800";
    } else {
      // Regular dates (not completed)
      classes += " text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700";
    }

    return classes;
  };

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className="w-full">
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={goToPreviousMonth}
          className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          title="Previous month"
        >
          <svg className="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <div className="flex items-center gap-2">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
            {monthName} {year}
          </h4>
          {!isSameDay(displayMonth, new Date()) && (
            <button
              onClick={goToCurrentMonth}
              className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
              title="Go to current month"
            >
              Today
            </button>
          )}
        </div>

        <button
          onClick={goToNextMonth}
          className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          title="Next month"
        >
          <svg className="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      {/* Week Day Headers */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {weekDays.map(day => (
          <div key={day} className="text-center text-xs font-medium text-gray-500 dark:text-gray-400 py-2">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-1">
        {calendarGrid.map((date, index) => (
          <div
            key={index}
            className={getDayClasses(date)}
            onClick={() => handleDateClick(date)}
            title={
              date >= firstDayOfMonth && date <= lastDayOfMonth
                ? `${date.toLocaleDateString()} - ${isDateCompleted(date) ? 'Completed' : 'Not completed'}`
                : ''
            }
          >
            {date.getDate()}
          </div>
        ))}
      </div>

      {/* Legend */}
      <div className="mt-4 flex items-center justify-center gap-4 text-xs text-gray-600 dark:text-gray-400">
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-green-500 dark:bg-green-600 rounded-full"></div>
          <span>Completed</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 border-2 border-blue-500 dark:border-blue-400 rounded-full"></div>
          <span>Today</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-gray-100 dark:bg-gray-700 rounded-full"></div>
          <span>Pending</span>
        </div>
      </div>
    </div>
  );
};

export default Calendar;
