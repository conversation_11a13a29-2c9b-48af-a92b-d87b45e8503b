// Test utilities for habit tracker functionality

import { 
  createHabit, 
  validateHabitName, 
  calculateCurrentStreak, 
  calculateLongestStreak, 
  calculateMonthlyTotal,
  toggleHabitCompletion,
  isHabitCompleted
} from '../utils/habitUtils.js';

import { 
  formatDate<PERSON>ey, 
  getTodayKey, 
  getDaysInMonth, 
  getCalendarGrid,
  isSameDay,
  isToday
} from '../utils/dateUtils.js';

import { getHabitData, saveHabitData } from '../utils/localStorage.js';

/**
 * Run basic functionality tests
 */
export const runBasicTests = () => {
  console.log('🧪 Running Habit Tracker Tests...\n');

  // Test 1: Habit Creation
  console.log('Test 1: Habit Creation');
  const habit1 = createHabit('Exercise');
  const habit2 = createHabit('Reading');
  console.log('✅ Created habits:', habit1.name, habit2.name);

  // Test 2: Habit Name Validation
  console.log('\nTest 2: Habit Name Validation');
  const validationTests = [
    { name: '', expected: false },
    { name: '   ', expected: false },
    { name: 'Valid Habit', expected: true },
    { name: 'A'.repeat(51), expected: false },
    { name: 'Exercise', expected: false } // Duplicate
  ];

  validationTests.forEach(test => {
    const result = validateHabitName(test.name, [habit1, habit2]);
    const passed = result.isValid === test.expected;
    console.log(`${passed ? '✅' : '❌'} "${test.name}" -> ${result.isValid} (expected: ${test.expected})`);
  });

  // Test 3: Date Utilities
  console.log('\nTest 3: Date Utilities');
  const today = new Date();
  const todayKey = getTodayKey();
  const formattedToday = formatDateKey(today);
  console.log('✅ Today key:', todayKey);
  console.log('✅ Formatted today:', formattedToday);
  console.log('✅ Is same day:', isSameDay(today, new Date()));
  console.log('✅ Is today:', isToday(today));

  // Test 4: Calendar Grid
  console.log('\nTest 4: Calendar Grid');
  const calendarGrid = getCalendarGrid(today);
  console.log('✅ Calendar grid length:', calendarGrid.length, '(should be 42)');
  console.log('✅ Days in current month:', getDaysInMonth(today).length);

  // Test 5: Completion Tracking
  console.log('\nTest 5: Completion Tracking');
  let completions = {};
  
  // Add some completions
  const dates = [
    new Date(2024, 8, 1), // Sept 1
    new Date(2024, 8, 2), // Sept 2
    new Date(2024, 8, 3), // Sept 3
    new Date(2024, 8, 5), // Sept 5 (skip 4)
    new Date(2024, 8, 6), // Sept 6
  ];

  dates.forEach(date => {
    completions = toggleHabitCompletion(habit1.id, formatDateKey(date), completions);
  });

  console.log('✅ Added completions for dates:', dates.map(d => formatDateKey(d)));

  // Test streak calculations
  const currentStreak = calculateCurrentStreak(habit1.id, completions);
  const longestStreak = calculateLongestStreak(habit1.id, completions);
  const monthlyTotal = calculateMonthlyTotal(habit1.id, completions, new Date(2024, 8, 1));

  console.log('✅ Current streak:', currentStreak);
  console.log('✅ Longest streak:', longestStreak);
  console.log('✅ Monthly total:', monthlyTotal);

  // Test 6: LocalStorage
  console.log('\nTest 6: LocalStorage');
  const testData = {
    habits: [habit1, habit2],
    completions: completions
  };

  saveHabitData(testData);
  const retrievedData = getHabitData();
  console.log('✅ Saved and retrieved data successfully');
  console.log('✅ Retrieved habits count:', retrievedData.habits.length);

  console.log('\n🎉 All tests completed!');
  return true;
};

/**
 * Test edge cases
 */
export const runEdgeCaseTests = () => {
  console.log('\n🔍 Running Edge Case Tests...\n');

  // Test leap year
  const leapYear = new Date(2024, 1, 1); // February 2024
  const leapYearDays = getDaysInMonth(leapYear);
  console.log('✅ Leap year February days:', leapYearDays.length, '(should be 29)');

  // Test month boundaries
  const endOfMonth = new Date(2024, 0, 31); // Jan 31
  const nextDay = new Date(2024, 1, 1); // Feb 1
  console.log('✅ Month boundary test:', !isSameDay(endOfMonth, nextDay));

  // Test empty completions
  const emptyStreak = calculateCurrentStreak('nonexistent', {});
  console.log('✅ Empty completions streak:', emptyStreak, '(should be 0)');

  console.log('\n🎉 Edge case tests completed!');
};

/**
 * Run all tests
 */
export const runAllTests = () => {
  try {
    runBasicTests();
    runEdgeCaseTests();
    return true;
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
};
