// Custom hook for managing habit data

import { useState, useEffect, useCallback } from 'react';
import { getHabitData, saveHabitData } from '../utils/localStorage.js';
import {
  createHabit,
  validateHabitName,
  toggleHabitCompletion,
  getHabitStats
} from '../utils/habitUtils.js';
import { formatDateKey } from '../utils/dateUtils.js';

/**
 * Custom hook for managing habits
 * @returns {Object} Habit management functions and state
 */
export const useHabits = () => {
  const [habits, setHabits] = useState([]);
  const [completions, setCompletions] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load data from localStorage on mount
  useEffect(() => {
    try {
      const data = getHabitData();
      setHabits(data.habits || []);
      setCompletions(data.completions || {});
      setIsLoading(false);
    } catch (err) {
      setError('Failed to load habit data');
      setIsLoading(false);
    }
  }, []);

  // Save data to localStorage whenever habits or completions change
  useEffect(() => {
    if (!isLoading) {
      try {
        saveHabitData({ habits, completions });
        setError(null);
      } catch (err) {
        setError('Failed to save habit data');
      }
    }
  }, [habits, completions, isLoading]);

  /**
   * Add a new habit
   * @param {string} name - Habit name
   * @returns {Object} Result with success status and error message
   */
  const addHabit = useCallback((name) => {
    const validation = validateHabitName(name, habits);
    
    if (!validation.isValid) {
      return { success: false, error: validation.error };
    }

    const newHabit = createHabit(name);
    setHabits(prev => [...prev, newHabit]);
    
    return { success: true, habit: newHabit };
  }, [habits]);

  /**
   * Delete a habit
   * @param {string} habitId - Habit ID to delete
   * @returns {Object} Result with success status
   */
  const deleteHabit = useCallback((habitId) => {
    try {
      setHabits(prev => prev.filter(habit => habit.id !== habitId));
      setCompletions(prev => {
        const newCompletions = { ...prev };
        delete newCompletions[habitId];
        return newCompletions;
      });
      return { success: true };
    } catch (err) {
      return { success: false, error: 'Failed to delete habit' };
    }
  }, []);

  /**
   * Update habit name
   * @param {string} habitId - Habit ID to update
   * @param {string} newName - New habit name
   * @returns {Object} Result with success status and error message
   */
  const updateHabitName = useCallback((habitId, newName) => {
    const otherHabits = habits.filter(habit => habit.id !== habitId);
    const validation = validateHabitName(newName, otherHabits);
    
    if (!validation.isValid) {
      return { success: false, error: validation.error };
    }

    setHabits(prev => prev.map(habit => 
      habit.id === habitId 
        ? { ...habit, name: newName.trim() }
        : habit
    ));
    
    return { success: true };
  }, [habits]);

  /**
   * Toggle habit completion for a specific date
   * @param {string} habitId - Habit ID
   * @param {Date} date - Date to toggle
   * @returns {Object} Result with success status
   */
  const toggleCompletion = useCallback((habitId, date) => {
    try {
      const dateKey = formatDateKey(date);
      setCompletions(prev => toggleHabitCompletion(habitId, dateKey, prev));
      return { success: true };
    } catch (err) {
      return { success: false, error: 'Failed to update completion' };
    }
  }, []);

  /**
   * Get habit statistics
   * @param {string} habitId - Habit ID
   * @param {Date} currentMonth - Current month for stats
   * @returns {Object} Habit statistics
   */
  const getStats = useCallback((habitId, currentMonth = new Date()) => {
    const habit = habits.find(h => h.id === habitId);
    if (!habit) return null;
    
    return getHabitStats(habit, completions, currentMonth);
  }, [habits, completions]);

  /**
   * Check if a habit is completed on a specific date
   * @param {string} habitId - Habit ID
   * @param {Date} date - Date to check
   * @returns {boolean} True if completed
   */
  const isCompleted = useCallback((habitId, date) => {
    const dateKey = formatDateKey(date);
    return !!(completions[habitId] && completions[habitId][dateKey]);
  }, [completions]);

  /**
   * Get all habits with their current statistics
   * @param {Date} currentMonth - Current month for stats
   * @returns {Array} Array of habits with stats
   */
  const getHabitsWithStats = useCallback((currentMonth = new Date()) => {
    return habits.map(habit => ({
      ...habit,
      stats: getHabitStats(habit, completions, currentMonth)
    }));
  }, [habits, completions]);

  return {
    // State
    habits,
    completions,
    isLoading,
    error,
    
    // Actions
    addHabit,
    deleteHabit,
    updateHabitName,
    toggleCompletion,
    
    // Getters
    getStats,
    isCompleted,
    getHabitsWithStats
  };
};
